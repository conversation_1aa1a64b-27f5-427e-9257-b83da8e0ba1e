package enums

// MessageContentType 消息内容类型显示文本
type MessageContentType int

const (
	MessageContentTypeImage MessageContentType = 2 // 图片消息
	MessageContentTypePost  MessageContentType = 3 // 帖子分享
	MessageContentTypeOrder MessageContentType = 4 // 订单消息
)

// String 返回消息内容类型的显示文本
func (t MessageContentType) String() string {
	switch t {
	case MessageContentTypeImage:
		return "[图片]"
	case MessageContentTypePost:
		return "[帖子分享]"
	case MessageContentTypeOrder:
		return "[订单消息]"
	default:
		return "[未知消息]"
	}
}

// IsValid 检查消息内容类型是否有效
func (t MessageContentType) IsValid() bool {
	switch t {
	case MessageContentTypeImage, MessageContentTypePost, MessageContentTypeOrder:
		return true
	default:
		return false
	}
}

// GetDisplayText 获取消息内容的显示文本
func (t MessageContentType) GetDisplayText() string {
	return t.String()
}
