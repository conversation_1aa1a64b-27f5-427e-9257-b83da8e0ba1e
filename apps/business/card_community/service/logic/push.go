package logic

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	"app_service/global"
	"context"
	"fmt"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

// PushOrderNotification 推送订单相关通知
// 根据订单状态变化向相关用户推送通知
func PushOrderNotification(ctx context.Context, req *define.PushOrderRequest) error {
	// 参数验证
	if req.UserID == "" {
		log.Ctx(ctx).Error("推送订单通知失败: 用户ID不能为空")
		return nil // 推送失败不影响主业务流程
	}
	if req.OrderID == "" {
		log.Ctx(ctx).Error("推送订单通知失败: 订单ID不能为空")
		return nil
	}
	if !req.PushType.IsValid() {
		log.Ctx(ctx).Error("推送订单通知失败: 推送类型无效")
		return nil
	}

	// 构造推送标题和内容
	title, content := buildOrderPushContent(req)

	// 构造extras，包含跳转URL
	extras := buildOrderExtras(req)

	// 调用推送服务发送通知，失败时进行重试
	if err := sendPushNotification(ctx, req.UserID, title, content, extras); err != nil {
		log.Ctx(ctx).Warnf("订单推送通知首次发送失败，开始重试 - 用户: %s, 错误: %v", req.UserID, err)

		// 使用重试机制
		if retryErr := retryPushNotification(ctx, req.UserID, title, content, extras); retryErr != nil {
			log.Ctx(ctx).Errorf("订单推送通知重试失败: %v", retryErr)
		}

		// 推送失败不返回错误，避免影响主业务流程
		return nil
	}

	log.Ctx(ctx).Infof("订单推送通知发送成功 - 用户: %s, 订单: %s, 类型: %s, 标题: %s",
		req.UserID, req.OrderID, req.PushType.String(), title)

	return nil
}

// PushMessageNotification 推送消息通知
// 向用户推送新消息通知
func PushMessageNotification(ctx context.Context, req *define.PushMessageRequest) error {
	// 参数验证
	if req.UserID == "" {
		log.Ctx(ctx).Error("推送消息通知失败: 用户ID不能为空")
		return nil // 推送失败不影响主业务流程
	}
	if req.MessageID == "" {
		log.Ctx(ctx).Error("推送消息通知失败: 消息ID不能为空")
		return nil
	}
	if !req.MessageType.IsValid() {
		log.Ctx(ctx).Error("推送消息通知失败: 消息类型无效")
		return nil
	}

	// 构造推送标题和内容
	title, content := buildMessagePushContent(req)

	// 构造extras，包含跳转URL
	extras := buildMessageExtras(req)

	// 调用推送服务发送通知，失败时进行重试
	if err := sendPushNotification(ctx, req.UserID, title, content, extras); err != nil {
		log.Ctx(ctx).Warnf("消息推送通知首次发送失败，开始重试 - 用户: %s, 错误: %v", req.UserID, err)

		// 使用重试机制
		if retryErr := retryPushNotification(ctx, req.UserID, title, content, extras); retryErr != nil {
			log.Ctx(ctx).Errorf("消息推送通知重试失败: %v", retryErr)
		}

		// 推送失败不返回错误，避免影响主业务流程
		return nil
	}

	log.Ctx(ctx).Infof("消息推送通知发送成功 - 用户: %s, 消息: %s, 类型: %s, 标题: %s",
		req.UserID, req.MessageID, req.MessageType.String(), title)

	return nil
}

// buildOrderPushContent 构造订单推送内容
// 根据订单推送类型构造相应的标题和内容
func buildOrderPushContent(req *define.PushOrderRequest) (title, content string) {
	// 使用枚举中定义的标题
	title = req.PushType.GetPushTitle()

	// 根据推送类型构造内容
	content = "来自 " + getFromUserName(req.FromUser)

	return title, content
}

// buildMessagePushContent 构造消息推送内容
// 根据消息类型构造相应的标题和内容
func buildMessagePushContent(req *define.PushMessageRequest) (title, content string) {
	// 构造标题
	title = "你有新的消息"

	// 根据消息类型构造内容
	switch req.MessageType {
	case enums.MessageTypeText:
		// 文本消息显示内容预览
		content = req.Content
		// 截取前50个字符并添加省略号
		runes := []rune(content)
		if len(runes) > 50 {
			content = string(runes[:50]) + "..."
		}
	case enums.MessageTypeImage:
		content = enums.MessageContentTypeImage.GetDisplayText()
	case enums.MessageTypePost:
		content = enums.MessageContentTypePost.GetDisplayText()
	case enums.MessageTypeOrder:
		content = enums.MessageContentTypeOrder.GetDisplayText()
	default:
		content = "您有新的消息"
	}

	return title, content
}

// buildOrderExtras 构造订单推送extras
// 参考PushMarketChangesMessage的模式，包含跳转URL
func buildOrderExtras(req *define.PushOrderRequest) map[string]interface{} {
	extras := make(map[string]interface{})

	// 构造订单详情页跳转URL
	pageURL := fmt.Sprintf("ojb://webview_h5?url=%s/pages/Card/OrderPayment/index?id=%s",
		getBaseURL(), req.OrderID)

	extras["url"] = pageURL

	return extras
}

// buildMessageExtras 构造消息推送extras
// 参考PushMarketChangesMessage的模式，包含跳转URL
func buildMessageExtras(req *define.PushMessageRequest) map[string]interface{} {
	extras := make(map[string]interface{})

	// 构造消息列表页跳转URL
	pageURL := fmt.Sprintf("ojb://webview_h5?url=%s/pages/Card/Message/index",
		getBaseURL())

	extras["url"] = pageURL

	return extras
}

// sendPushNotification 发送推送通知的统一方法
// 集成极光推送服务，发送推送通知到用户设备
func sendPushNotification(ctx context.Context, userID, title, content string, extras map[string]interface{}) error {
	// 构造推送消息参数
	message := notidefine.PushMessage{
		Title:         title,
		Content:       content,
		AudienceType:  notienums.PushAudienceTypeUser, // 按用户推送
		UserIDs:       []string{userID},               // 目标用户ID列表
		AndroidExtras: extras,                         // Android端额外数据
		IOSExtras:     extras,                         // iOS端额外数据
	}

	// 构造推送关联信息，用于记录和去重
	relateInfo := notidefine.PushRelateInfo{
		RelateType:  notienums.PushRelateTypeCardCommunityOrder,      // 关联类型：卡牌社区订单
		RelateScene: notienums.PushRelateSceneStatusChange,           // 关联场景：状态变更
		RelateID:    fmt.Sprintf("%s_%d", userID, time.Now().Unix()), // 关联ID：用户ID_时间戳，确保唯一性
	}

	// 调用通知门面服务发送推送
	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		log.Ctx(ctx).Errorf("极光推送发送失败 - 用户: %s, 标题: %s, 错误: %v", userID, title, err)
		return err
	}

	// 记录推送成功日志
	log.Ctx(ctx).Infof("极光推送发送成功 - 用户: %s, 标题: %s, 内容: %s, 推送总数: %d, 成功: %d, 失败: %d, 消息ID: %s",
		userID, title, content, result.SendTotal, result.SuccessCount, result.FailCount, result.MessageID)

	return nil
}

// retryPushNotification 推送通知重试机制
// 当推送失败时，进行重试处理，使用指数退避策略
func retryPushNotification(ctx context.Context, userID, title, content string, extras map[string]interface{}) error {
	maxRetries := 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		// 构造推送消息参数
		message := notidefine.PushMessage{
			Title:         title,
			Content:       content,
			AudienceType:  notienums.PushAudienceTypeUser,
			UserIDs:       []string{userID},
			AndroidExtras: extras,
			IOSExtras:     extras,
		}

		// 构造推送关联信息，每次重试使用不同的关联ID避免去重限制
		relateInfo := notidefine.PushRelateInfo{
			RelateType:  notienums.PushRelateTypeCardCommunityOrder,
			RelateScene: notienums.PushRelateSceneRetryPush,
			RelateID:    fmt.Sprintf("%s_retry_%d_%d", userID, i+1, time.Now().UnixNano()),
		}

		// 尝试发送推送
		result, err := notifacade.PushMessage(ctx, message, relateInfo)
		if err == nil {
			// 推送成功
			if i > 0 {
				log.Ctx(ctx).Infof("推送通知重试成功 - 用户: %s, 重试次数: %d, 推送总数: %d, 成功: %d",
					userID, i+1, result.SendTotal, result.SuccessCount)
			}
			return nil
		}

		// 记录错误
		lastErr = err
		log.Ctx(ctx).Warnf("推送通知第 %d 次尝试失败 - 用户: %s, 错误: %v", i+1, userID, err)

		// 如果不是最后一次重试，等待后再试
		if i < maxRetries-1 {
			// 使用指数退避策略：1s, 2s, 4s
			waitTime := time.Duration(1<<uint(i)) * time.Second
			log.Ctx(ctx).Infof("推送通知将在 %v 后进行第 %d 次重试 - 用户: %s", waitTime, i+2, userID)
			time.Sleep(waitTime)
		}
	}

	// 所有重试都失败
	log.Ctx(ctx).Errorf("推送通知重试失败 - 用户: %s, 已重试 %d 次, 最后错误: %v", userID, maxRetries, lastErr)
	return fmt.Errorf("推送通知重试失败，已达到最大重试次数 %d，最后错误: %v", maxRetries, lastErr)
}

// getFromUserName 获取来源用户名称
// 如果用户名为空，返回默认名称
func getFromUserName(fromUser string) string {
	if fromUser == "" {
		return "用户"
	}
	return fromUser
}

// getBaseURL 获取基础URL
// 根据环境返回对应的域名
func getBaseURL() string {
	baseURL := "https://sit-wcjs.ahbq.com.cn"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		baseURL = "https://wcjs.ahbq.com.cn"
	}
	return baseURL
}
