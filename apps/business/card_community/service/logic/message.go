package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// ValidateMessageContent 验证消息内容
// 根据消息类型验证对应的内容是否符合要求
func ValidateMessageContent(req *define.SendMessageReq) error {
	switch req.MessageType {
	case enums.MessageTypeText:
		// 文本消息必须有内容
		if req.Content == "" {
			return commondefine.ParamErr.SetMsg("文本消息必须包含内容")
		}
	case enums.MessageTypeImage:
		// 图片消息必须有媒体文件
		if req.Media == nil {
			return commondefine.ParamErr.SetMsg("图片消息必须包含媒体文件")
		}
		if req.Media.Type != enums.MediaTypeImage {
			return commondefine.ParamErr.SetMsg("媒体文件类型必须为图片")
		}
	case enums.MessageTypePost:
		// 帖子消息必须有帖子ID和快照
		if req.PostID == "" {
			return commondefine.ParamErr.SetMsg("帖子消息必须包含帖子ID")
		}
		if req.PostSnapshot == nil {
			return commondefine.ParamErr.SetMsg("帖子消息必须包含帖子快照")
		}
		//case enums.MessageTypeOrder:
		//	// 订单消息必须有订单ID和快照
		//	if req.OrderID == "" {
		//		return commondefine.ParamErr.SetMsg("订单消息必须包含订单ID")
		//	}
		//	if req.OrderSnapshot == nil {
		//		return commondefine.ParamErr.SetMsg("订单消息必须包含订单快照")
		//	}
	}

	return nil
}

// GenerateMessageSummary 生成消息摘要
// 根据消息类型和内容生成用于会话列表显示的摘要文本
func GenerateMessageSummary(content string, messageType enums.MessageType, status *enums.OrderStatus) string {
	switch messageType {
	case enums.MessageTypeText:
		// 文本消息直接返回内容，如果太长则截取
		if len(content) > 50 {
			return content[:50] + "..."
		}
		return content
	case enums.MessageTypeImage:
		return "[图片]"
	case enums.MessageTypePost:
		return "[帖子]"
	case enums.MessageTypeOrder:
		if status == nil {
			return "[订单]"
		}
		switch *status {
		case enums.OrderStatusUnPaid:
			return enums.OrderMessageTypeUnPaid.GetPushTitle()
		case enums.OrderStatusUnDelivered:
			return enums.OrderMessageTypeUnDelivered.GetPushTitle()
		default:
			return "[订单]"
		}
	default:
		return "[消息]"
	}
}

// UpdateConversationLastMessage 更新会话的最后消息信息
// 同时更新发送者和接收者的会话记录，包括最后消息内容和未读数
// 同时处理消息发送状态（只有非智能回复才设置状态）
// 当消息类型为帖子快照时，同时更新会话的帖子ID
func UpdateConversationLastMessage(ctx context.Context, tx *gorm.DB, senderID, receiverID, messageID, content string, messageType enums.MessageType, isAutoReply bool,
	postID *string, orderStatus *enums.OrderStatus) error {
	// 生成消息摘要
	summary := GenerateMessageSummary(content, messageType, orderStatus)

	now := time.Now()
	updateData := map[string]interface{}{
		"last_message_id":      messageID,
		"last_message_content": summary,
		"last_message_time":    &now,
		"last_message_type":    int32(messageType),
		"updated_at":           now,
	}

	// 如果是帖子消息类型且提供了帖子ID，则更新会话的帖子ID
	if messageType == enums.MessageTypePost && postID != nil && *postID != "" {
		updateData["post_id"] = *postID
	}

	// 更新发送者的会话记录
	senderUpdateResult := tx.Model(&model.Conversation{}).
		Where("participant_id = ? AND other_participant_id = ?", senderID, receiverID).
		Updates(updateData)
	if senderUpdateResult.Error != nil {
		log.Ctx(ctx).Errorf("更新发送者会话记录失败: %v", senderUpdateResult.Error)
		return commondefine.CommonErr
	}
	if senderUpdateResult.RowsAffected == 0 {
		log.Ctx(ctx).Errorf("发送者会话记录不存在或未更新")
		return commondefine.CommonErr
	}

	// 更新接收者的会话记录（增加未读数，非智能回复时设置状态）
	receiverUpdateData := map[string]interface{}{
		"last_message_id":      messageID,
		"last_message_content": summary,
		"last_message_time":    &now,
		"last_message_type":    int32(messageType),
		"unread_count":         gorm.Expr("unread_count + 1"),
		"updated_at":           now,
	}

	// 如果是帖子消息类型且提供了帖子ID，则更新会话的帖子ID
	if messageType == enums.MessageTypePost && postID != nil && *postID != "" {
		receiverUpdateData["post_id"] = *postID
	}

	// 只有非智能回复时才设置接收者状态为正常
	if !isAutoReply {
		receiverUpdateData["status"] = enums.StatusNormal.Int32() // 接收者收到消息后设置为正常
	}

	// 更新接收者会话记录
	receiverUpdateResult := tx.Model(&model.Conversation{}).
		Where("participant_id = ? AND other_participant_id = ?", receiverID, senderID).
		Updates(receiverUpdateData)
	if receiverUpdateResult.Error != nil {
		log.Ctx(ctx).Errorf("更新接收者会话记录失败: %v", receiverUpdateResult.Error)
		return commondefine.CommonErr
	}
	if receiverUpdateResult.RowsAffected == 0 {
		log.Ctx(ctx).Errorf("接收者会话记录不存在或未更新")
		return commondefine.CommonErr
	}

	return nil
}

// CheckMessageIdempotent 检查消息幂等性（仅使用缓存）
// 返回值：existingMessage, error
func CheckMessageIdempotent(ctx context.Context, clientMsgID string) (*define.SendMessageResp, error) {
	// 检查缓存
	cacheKey := constant.GetMessageIdempotentKey(clientMsgID)
	cachedData, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil {
		// 缓存未命中，当作新消息处理
		return nil, nil
	}

	// 缓存命中，解析数据
	var cachedResp define.SendMessageResp
	if err := json.Unmarshal([]byte(cachedData), &cachedResp); err != nil {
		// 缓存数据解析失败，记录日志并当作新消息处理
		log.Ctx(ctx).Warnf("解析消息幂等性缓存失败: %v", err)
		return nil, nil
	}

	return &cachedResp, nil
}

// SetMessageIdempotentCache 设置消息幂等性缓存
func SetMessageIdempotentCache(ctx context.Context, clientMsgID string, resp *define.SendMessageResp) error {
	cacheKey := constant.GetMessageIdempotentKey(clientMsgID)
	jsonData, err := json.Marshal(resp)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化消息幂等性缓存数据失败: %v", err)
		return err
	}

	// 同步设置缓存，避免并发问题
	if err := global.REDIS.Set(ctx, cacheKey, jsonData, constant.MessageIdempotentTTL).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置消息幂等性缓存失败: %v", err)
		return err
	}

	return nil
}

// GetParticipantIDsByPostID 根据帖子ID获取参与者ID列表
func GetParticipantIDsByPostID(ctx context.Context, postID string) ([]string, error) {
	messageSchema := repo.GetQuery().Message
	messageQueryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.PostID, postID)

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).SelectList(messageQueryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("根据帖子ID查询消息失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	if len(messages) == 0 {
		return []string{}, nil
	}

	// 收集参与者ID
	participantIDSet := make(map[string]bool)
	for _, msg := range messages {
		participantIDSet[msg.BigUserID] = true
		participantIDSet[msg.SmallUserID] = true
	}

	// 将参与者ID转换为切片
	var participantIDs []string
	for id := range participantIDSet {
		participantIDs = append(participantIDs, id)
	}

	return participantIDs, nil
}

// GetUserIDsByKeyword 根据关键字搜索消息内容获取用户ID列表
func GetUserIDsByKeyword(ctx context.Context, keyword string) ([]string, error) {
	messageSchema := repo.GetQuery().Message
	messageQueryBuilder := search.NewQueryBuilder().
		Like(messageSchema.Content, "%"+keyword+"%")

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).SelectList(messageQueryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("根据关键字搜索消息内容失败: %v", err)
		return nil, commondefine.CommonErr.Err(err)
	}

	if len(messages) == 0 {
		return []string{}, nil
	}

	// 收集消息中的用户ID
	userIDSet := make(map[string]bool)
	for _, msg := range messages {
		userIDSet[msg.BigUserID] = true
		userIDSet[msg.SmallUserID] = true
	}

	// 转换为切片
	var result []string
	for userID := range userIDSet {
		result = append(result, userID)
	}

	return result, nil
}

// GetConversationMessageCounts 批量统计会话的消息数量
func GetConversationMessageCounts(ctx context.Context, conversations []*model.Conversation) (map[string]int, error) {
	messageCountMap := make(map[string]int)

	for _, conv := range conversations {
		// 计算用户对的大小用户ID
		bigUserID, smallUserID, _ := model.CalculateUserPair(conv.ParticipantID, conv.OtherParticipantID)

		messageSchema := repo.GetQuery().Message
		count, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).Count(
			search.NewQueryBuilder().
				Eq(messageSchema.BigUserID, bigUserID).
				Eq(messageSchema.SmallUserID, smallUserID).
				Build(),
		)
		if err != nil {
			log.Ctx(ctx).Warnf("统计会话消息数量失败，会话ID: %s, 错误: %v", conv.ID, err)
			messageCountMap[conv.ID] = 0
		} else {
			messageCountMap[conv.ID] = int(count)
		}
	}

	return messageCountMap, nil
}

// CheckMessageFrequencyLimit 检查消息发送频率限制
// 如果会话处于频率限制状态，则统计发送者向接收者发送的连续消息数量
// 如果超过5条则返回错误
func CheckMessageFrequencyLimit(ctx context.Context, conversation *model.Conversation, senderID, receiverID string) error {
	// 如果会话没有限制标记，直接通过
	if !conversation.IsLimitedStatus() {
		return nil
	}

	// 统计发送者向接收者发送的连续消息数量（从最后一条对方发送的消息之后开始计算）
	count, err := countConsecutiveMessages(ctx, senderID, receiverID)
	if err != nil {
		log.Ctx(ctx).Errorf("统计连续消息数量失败: %v", err)
		return commondefine.CommonErr
	}

	// 如果已经发送了5条或更多消息，则阻止发送
	if count >= 5 {
		return define.CC500208Err
	}

	return nil
}

// countConsecutiveMessages 统计发送者向接收者发送的消息数量
func countConsecutiveMessages(ctx context.Context, senderID, receiverID string) (int64, error) {
	// 计算用户对的大小用户ID
	bigUserID, smallUserID, senderDirection := model.CalculateUserPair(senderID, receiverID)

	messageSchema := repo.GetQuery().Message

	// 统计发送者发送的所有消息
	queryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.BigUserID, bigUserID).
		Eq(messageSchema.SmallUserID, smallUserID).
		Eq(messageSchema.Direction, senderDirection)

	// 统计消息数量
	count, err := repo.NewMessageRepo(messageSchema.WithContext(ctx)).Count(queryBuilder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("统计消息数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// getClientMsgNumberByMessageID 根据消息ID查询该消息的ClientMsgNumber
func getClientMsgNumberByMessageID(ctx context.Context, messageID string) (int64, error) {
	var clientMsgNumber int64

	// 如果messageID为空，返回0（表示这是第一条消息）
	if messageID == "" {
		return 0, nil
	}

	// 直接查询指定消息的client_msg_number
	err := repo.GetDB().WithContext(ctx).
		Model(&model.Message{}).
		Select("COALESCE(client_msg_number, 0)").
		Where("id = ?", messageID).
		Scan(&clientMsgNumber).Error

	if err != nil {
		log.Ctx(ctx).Errorf("查询消息ClientMsgNumber失败: messageID=%s, error=%v", messageID, err)
		return 0, err
	}

	log.Ctx(ctx).Debugf("查询到消息ClientMsgNumber: messageID=%s, clientMsgNumber=%d", messageID, clientMsgNumber)
	return clientMsgNumber, nil
}

// SendOrderMessageOnOrderChange 在订单变动时发送订单消息
// 参数：
// - conversationGroupID: 会话组ID
// - senderID: 消息发送者
// - receiverID: 消息接受者
// - orderID: 订单ID
// - orderSnapshot: 订单快照信息
// - lastMessageID: 会话最后一条消息ID，用于获取ClientMsgNumber
func SendOrderMessageOnOrderChange(ctx context.Context, conversationGroupID, senderID, receiverID, orderID string, orderSnapshot *define.OrderSnapshot, lastMessageID string) error {
	// 计算用户对的大小用户ID和消息方向
	bigUserID, smallUserID, direction := model.CalculateUserPair(senderID, receiverID)

	// 查询最后一条消息的ClientMsgNumber
	lastClientMsgNumber, err := getClientMsgNumberByMessageID(ctx, lastMessageID)
	if err != nil {
		log.Ctx(ctx).Errorf("查询最后消息ClientMsgNumber失败: %v", err)
		return err
	}

	// 新消息的ClientMsgNumber = 最后一条消息的ClientMsgNumber
	newClientMsgNumber := lastClientMsgNumber

	// 生成消息ID
	messageID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	clientMsgID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)

	// 创建订单消息
	message := &model.Message{
		ID:          messageID,
		ClientMsgID: clientMsgID,
		SmallUserID: smallUserID,
		BigUserID:   bigUserID,
		Direction:   direction,
		MessageType: int32(enums.MessageTypeOrder),
		//Content:         messageContent,     // 使用传入的消息内容
		OrderID:         orderID,            // 设置订单ID
		IsSmartReply:    false,              // 订单消息不是智能回复
		ClientMsgNumber: newClientMsgNumber, // 使用计算出的序号
		// 消息的客户端信息设置为空，因为是系统生成的
		AppChannel: "",
		AppVersion: "",
		ClientType: "",
		IP:         "",
	}

	if err := message.SetOrderSnapshot(orderSnapshot); err != nil {
		log.Ctx(ctx).Errorf("设置订单快照失败: %v", err)
		return err
	}

	// 使用独立事务处理订单消息发送
	return repo.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 保存订单消息
		if err := tx.Create(message).Error; err != nil {
			log.Ctx(ctx).Errorf("保存订单消息失败: %v", err)
			return err
		}

		// 更新会话的最后消息信息
		// receiverID=商家（订单消息的发送者），senderID=用户（订单消息的接收者）
		// 订单消息传入false，设置接收者状态，订单消息涉及订单ID
		if err := UpdateConversationLastMessage(ctx, tx, receiverID, senderID,
			messageID, "", enums.MessageTypeOrder, false, nil, &orderSnapshot.Status); err != nil {
			return err
		}

		log.Ctx(ctx).Infof("订单消息发送成功: conversationGroupID=%s, messageID=%s, orderID=%s, merchantID=%s", conversationGroupID, messageID, orderID, receiverID)
		return nil
	})
}

// PushMessageNotification 推送消息通知
// 向用户推送新消息通知
func PushMessageNotification(ctx context.Context, req *define.PushMessageRequest, relateInfo notidefine.PushRelateInfo) error {
	// 参数验证
	if req.UserID == "" {
		log.Ctx(ctx).Error("推送消息通知失败: 用户ID不能为空")
		return nil // 推送失败不影响主业务流程
	}
	if req.MessageID == "" {
		log.Ctx(ctx).Error("推送消息通知失败: 消息ID不能为空")
		return nil
	}
	if !req.MessageType.IsValid() {
		log.Ctx(ctx).Error("推送消息通知失败: 消息类型无效")
		return nil
	}

	// 构造推送标题和内容
	title, content := buildMessagePushContent(req)

	// 构造extras，包含跳转URL
	extras := buildMessageExtras(req)

	// 直接调用推送服务发送通知
	if err := sendMessagePushNotification(ctx, req.UserID, title, content, extras, relateInfo); err != nil {
		log.Ctx(ctx).Errorf("消息推送通知发送失败 - 用户: %s, 错误: %v", req.UserID, err)
		return nil // 推送失败不影响主业务流程
	}

	log.Ctx(ctx).Infof("消息推送通知发送成功 - 用户: %s, 消息: %s, 类型: %s, 标题: %s",
		req.UserID, req.MessageID, req.MessageType.String(), title)

	return nil
}

// buildMessagePushContent 构造消息推送内容
// 根据消息类型构造相应的标题和内容
func buildMessagePushContent(req *define.PushMessageRequest) (title, content string) {
	// 构造标题
	title = "你有新的消息"

	content = GenerateMessageSummary(req.Content, req.MessageType, nil)

	return title, content
}

// buildMessageExtras 构造消息推送extras
// 参考PushMarketChangesMessage的模式，包含跳转URL
func buildMessageExtras(req *define.PushMessageRequest) map[string]interface{} {
	extras := make(map[string]interface{})

	// 构造消息列表页跳转URL
	pageURL := fmt.Sprintf("ojb://webview_h5?url=%s/pages/Card/Message/index",
		getBaseURL())

	extras["url"] = pageURL

	return extras
}

// sendMessagePushNotification 发送消息推送通知的方法
// 集成极光推送服务，发送推送通知到用户设备
func sendMessagePushNotification(ctx context.Context, userID, title, content string, extras map[string]interface{}, relateInfo notidefine.PushRelateInfo) error {
	// 构造推送消息参数
	message := notidefine.PushMessage{
		Title:         title,
		Content:       content,
		AudienceType:  notienums.PushAudienceTypeUser, // 按用户推送
		UserIDs:       []string{userID},               // 目标用户ID列表
		AndroidExtras: extras,                         // Android端额外数据
		IOSExtras:     extras,                         // iOS端额外数据
	}

	// 调用通知门面服务发送推送
	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		log.Ctx(ctx).Errorf("极光推送发送失败 - 用户: %s, 标题: %s, 错误: %v", userID, title, err)
		return err
	}

	// 记录推送成功日志
	log.Ctx(ctx).Infof("极光推送发送成功 - 用户: %s, 标题: %s, 内容: %s, 推送总数: %d, 成功: %d, 失败: %d, 消息ID: %s",
		userID, title, content, result.SendTotal, result.SuccessCount, result.FailCount, result.MessageID)

	return nil
}

// getMessageBaseURL 获取基础URL
// 根据环境返回对应的域名
func getMessageBaseURL() string {
	baseURL := "https://sit-wcjs.ahbq.com.cn"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		baseURL = "https://wcjs.ahbq.com.cn"
	}
	return baseURL
}
